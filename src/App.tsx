import React, { useState, useEffect } from 'react';
import { ConfigProvider, App as AntdApp, Button, Card, Layout } from 'antd';
import zhCN from 'antd/locale/zh_CN';







const App: React.FC = () => {
  console.log('🚀 App component is rendering!');

  return (
    <ConfigProvider locale={zhCN}>
      <AntdApp>
        <div style={{
          padding: '20px',
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
          fontFamily: 'Arial, sans-serif'
        }}>
          <Card
            title="智阅AI - 系统状态检查"
            style={{
              maxWidth: '800px',
              margin: '0 auto',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}
          >
            <div style={{ lineHeight: '2' }}>
              <p>✅ React 应用正常运行</p>
              <p>✅ Vite 开发服务器正常</p>
              <p>✅ Ant Design 组件库加载成功</p>
              <p>✅ TypeScript 编译正常</p>
              <p>🕐 当前时间: {new Date().toLocaleString('zh-CN')}</p>
              <p>🌐 访问地址: {window.location.href}</p>

              <div style={{ marginTop: '20px' }}>
                <Button
                  type="primary"
                  size="large"
                  onClick={() => {
                    alert('按钮功能正常！\n\n如果您能看到这个弹窗，说明JavaScript交互功能正常工作。');
                  }}
                >
                  测试交互功能
                </Button>

                <Button
                  style={{ marginLeft: '10px' }}
                  onClick={() => {
                    console.log('控制台日志测试');
                    console.log('当前环境:', import.meta.env.MODE);
                    console.log('API地址:', import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000');
                    alert('请按F12打开开发者工具查看控制台日志');
                  }}
                >
                  测试控制台日志
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;