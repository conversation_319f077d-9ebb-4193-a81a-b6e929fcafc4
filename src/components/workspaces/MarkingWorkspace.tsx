import React, { useState } from 'react';
import { Card, Row, Col, Breadcrumb, Button, Alert, Progress, Statistic, Empty, message, Tabs, Tag, Modal, InputNumber } from 'antd';
import { UserOutlined, FileTextOutlined, CheckCircleOutlined, SaveOutlined, FastForwardOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Sparkles } from 'lucide-react';
import { useAppContext } from '../../contexts/AppContext';
import { Exam } from '../../types/exam';
import { mockConfigureData } from '../../data/mockData';

interface MarkingWorkspaceProps {
  exam: Exam;
}

// 模拟学生答题数据
const mockStudentAnswers = [
  {
    id: '2024001',
    name: '王同学',
    status: 'current',
    answers: {
      q13: '1953年，武汉长江大桥建成通车，这是新中国成立后的重大工程成就。1964年，邓稼先参与了原子弹的研制工作。三大改造采用了公私合营的方式，这是一个重要的创举。',
      q14: '中国梦的蓝图是实现国家富强、民族振兴、人民幸福。在国防建设方面，成立了火箭军这一新军种，并设立了五大战区。',
      q15: '民族区域自治制度促进了西藏地区的经济发展。一国两制在香港和澳门得到了成功实践。'
    },
    aiScores: {
      q13: { total: 9, suggestions: '答案基本正确，但对三大改造的影响阐述不够深入' },
      q14: { total: 7, suggestions: '核心概念理解正确，但军民关系的论述可以更深入' },
      q15: { total: 10, suggestions: '各个要点都有涉及，表述准确' }
    }
  },
  {
    id: '2024002',
    name: '李同学',
    status: 'pending',
    answers: {
      q13: '武汉长江大桥是1953年建成的，邓稼先是原子弹之父。公私合营是三大改造的方式。',
      q14: '中国梦就是要实现中华民族伟大复兴。火箭军是新成立的军种。',
      q15: '西藏实行民族区域自治。香港澳门回归祖国。'
    },
    aiScores: {
      q13: { total: 6, suggestions: '要点提及但不够完整，缺少对历史影响的分析' },
      q14: { total: 5, suggestions: '基本概念正确但表述过于简单' },
      q15: { total: 7, suggestions: '要点正确但缺少深入分析' }
    }
  },
  {
    id: '2024003',
    name: '张同学',
    status: 'pending',
    answers: {
      q13: '1953年武汉长江大桥，1964年邓稼先研制原子弹。三大改造通过公私合营方式，实现了生产资料所有制的社会主义改造，建立了社会主义制度。家庭联产承包责任制解放了农村生产力。',
      q14: '中国梦是实现国家富强、民族振兴、人民幸福的伟大梦想。火箭军的成立和五大战区的设立体现了国防现代化。军队建设为经济发展提供安全保障。',
      q15: '民族区域自治制度在西藏等地区的实施促进了经济社会发展。一国两制在香港澳门的成功实践为台海问题提供了借鉴。独立自主的和平外交政策体现了中国的外交智慧。'
    },
    aiScores: {
      q13: { total: 11, suggestions: '答案完整准确，各个要点都有涉及' },
      q14: { total: 10, suggestions: '理解深入，表述准确' },
      q15: { total: 13, suggestions: '分析全面，逻辑清晰' }
    }
  },
  {
    id: '2024004',
    name: '刘同学',
    status: 'completed',
    finalScores: { q13: 8, q14: 6, q15: 9 }
  },
  {
    id: '2024005',
    name: '陈同学',
    status: 'completed',
    finalScores: { q13: 10, q14: 8, q15: 11 }
  }
];

const MarkingWorkspace: React.FC<MarkingWorkspaceProps> = ({ exam }) => {
  const { setSubViewInfo, updateExamStatus } = useAppContext();
  const [currentStudentIndex, setCurrentStudentIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentQuestionId, setCurrentQuestionId] = useState('q13');
  const [manualScores, setManualScores] = useState<Record<string, number>>({});
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const currentStudent = mockStudentAnswers[currentStudentIndex];
  const completedCount = mockStudentAnswers.filter(s => s.status === 'completed').length;
  const progressPercent = Math.round((completedCount / mockStudentAnswers.length) * 100);

  const handleBack = () => {
    setSubViewInfo({ view: null, exam: null });
  };

  const handleNextStudent = () => {
    if (currentStudentIndex < mockStudentAnswers.length - 1) {
      setCurrentStudentIndex(prev => prev + 1);
      setCurrentQuestionId('q13');
      setManualScores({});
    }
  };

  const handlePrevStudent = () => {
    if (currentStudentIndex > 0) {
      setCurrentStudentIndex(prev => prev - 1);
      setCurrentQuestionId('q13');
      setManualScores({});
    }
  };

  const handleSubmitGrading = () => {
    setShowConfirmModal(true);
  };

  const confirmSubmitGrading = () => {
    setLoading(true);
    
    // 模拟提交评分
    setTimeout(() => {
      // 更新学生状态
      mockStudentAnswers[currentStudentIndex].status = 'completed';
      
      message.success(`${currentStudent.name}的评分已提交！`);
      setLoading(false);
      setShowConfirmModal(false);
      
    // 自动跳转到下一个学生
      if (currentStudentIndex < mockStudentAnswers.length - 1) {
        handleNextStudent();
      } else {
        // 如果是最后一个学生，提示完成阅卷
        Modal.confirm({
          title: '阅卷完成',
          content: '所有学生的答卷已评阅完成，是否生成分析报告？',
          okText: '生成报告',
          cancelText: '稍后处理',
          onOk: () => {
            updateExamStatus(exam.id, '已完成');
            message.success('阅卷完成！正在生成分析报告...');
            setTimeout(() => {
              setSubViewInfo({ view: 'analysis', exam: { ...exam, status: '已完成' } });
            }, 1500);
          }
        });
      }
    }, 1000);
  };

  const handleSkipStudent = () => {
    message.info('已跳过当前学生，稍后可在异常处理中继续评阅');
      handleNextStudent();
  };

  const getQuestionScore = (questionId: string) => {
    if (manualScores[questionId] !== undefined) {
      return manualScores[questionId];
    }
    const aiScore = currentStudent.aiScores?.[questionId as keyof typeof currentStudent.aiScores];
    return aiScore?.total || 0;
  };

  const getTotalScore = () => {
    return mockConfigureData.questions.reduce((total, q) => total + getQuestionScore(q.id), 0);
  };

  const renderQuestionTabs = () => {
    const items = mockConfigureData.questions.map(question => ({
      key: question.id,
      label: (
        <div className="flex items-center gap-2">
          <span>{question.title.split('：')[0]}</span>
          <Tag color={manualScores[question.id] !== undefined ? 'green' : 'blue'}>
            {getQuestionScore(question.id)}/{question.points}
          </Tag>
        </div>
      ),
      children: (
        <div className="space-y-4">
          {/* 学生答案 */}
          <Card size="small" title="学生答案">
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="whitespace-pre-wrap text-gray-800">
                {currentStudent.answers?.[question.id as keyof typeof currentStudent.answers] || '暂无答案'}
              </p>
            </div>
          </Card>

          {/* AI评分建议 */}
          {currentStudent.aiScores?.[question.id as keyof typeof currentStudent.aiScores] && (
            <Card size="small" title={
              <div className="flex items-center gap-2">
                <Sparkles className="w-4 h-4 text-purple-500" />
                <span>AI评分建议</span>
              </div>
            }>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span>AI建议得分：</span>
                  <Tag color="purple" className="text-lg">
                    {currentStudent.aiScores[question.id as keyof typeof currentStudent.aiScores]?.total}/{question.points}
                  </Tag>
                </div>
                <div className="text-sm text-gray-600">
                  {currentStudent.aiScores[question.id as keyof typeof currentStudent.aiScores]?.suggestions}
                </div>
              </div>
            </Card>
          )}

          {/* 手动评分 */}
          <Card size="small" title="手动评分">
            <div className="flex items-center gap-4">
              <span>最终得分：</span>
              <InputNumber
                min={0}
                max={question.points}
                value={getQuestionScore(question.id)}
                onChange={(value) => setManualScores(prev => ({ ...prev, [question.id]: value || 0 }))}
                addonAfter={`/${question.points}`}
              />
              <Button
                size="small"
                onClick={() => {
                  const aiScore = currentStudent.aiScores?.[question.id as keyof typeof currentStudent.aiScores];
                  if (aiScore) {
                    setManualScores(prev => ({ ...prev, [question.id]: aiScore.total }));
                  }
                }}
              >
                采用AI建议
              </Button>
            </div>
          </Card>
        </div>
      )
    }));

    return (
      <Tabs
        activeKey={currentQuestionId}
        onChange={setCurrentQuestionId}
        items={items}
        className="h-full"
      />
    );
  };

  if (currentStudent.status === 'completed') {
    return (
      <div className="flex items-center justify-center h-96">
        <Empty
          description="该学生已完成评阅"
        >
          <div className="space-x-2">
            <Button onClick={handlePrevStudent} disabled={currentStudentIndex === 0}>
              上一位学生
            </Button>
            <Button type="primary" onClick={handleNextStudent} disabled={currentStudentIndex === mockStudentAnswers.length - 1}>
              下一位学生
            </Button>
          </div>
        </Empty>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <Breadcrumb
          items={[
            { title: <a onClick={handleBack}>阅卷中心</a> },
            { title: exam.name },
            { title: '人机协同阅卷' }
          ]}
        />
        
        {/* 阅卷进度 */}
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-500">
            阅卷进度: {completedCount}/{mockStudentAnswers.length}
          </div>
          <Progress 
            percent={progressPercent} 
            size="small" 
            className="w-32"
            strokeColor="#52c41a"
          />
        </div>
      </div>

      {/* 当前学生信息 */}
      <Alert
        message={
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <UserOutlined className="text-blue-600" />
              <span>当前评阅: <strong>{currentStudent.name}</strong> ({currentStudent.id})</span>
            </div>
            <div className="flex items-center gap-2">
              <Tag color="blue">第 {currentStudentIndex + 1} 份</Tag>
              <Tag color="green">剩余 {mockStudentAnswers.length - currentStudentIndex - 1} 份</Tag>
            </div>
          </div>
        }
        type="info"
        className="mb-4"
      />

      <Row gutter={[24, 24]}>
        {/* 左侧：答题内容 */}
        <Col xs={24} lg={14}>
          <Card 
            title={
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileTextOutlined className="text-blue-600" />
                  <span>答题内容</span>
                </div>
                <div className="flex items-center gap-2">
                  <Button 
                    size="small"
                    icon={<LeftOutlined />}
                    disabled={currentStudentIndex === 0}
                    onClick={handlePrevStudent}
                  >
                    上一份
                  </Button>
                  <Button 
                    size="small"
                    icon={<RightOutlined />}
                    disabled={currentStudentIndex === mockStudentAnswers.length - 1}
                    onClick={handleNextStudent}
                  >
                    下一份
                  </Button>
                </div>
              </div>
            }
            className="h-full"
          >
            {renderQuestionTabs()}
          </Card>
        </Col>

        {/* 右侧：评分统计 */}
        <Col xs={24} lg={10}>
          <Card
            title={
              <div className="flex items-center gap-2">
                <Sparkles className="text-purple-500" />
                <span>评分统计</span>
              </div>
            }
            className="h-full"
          >
            {/* 总分统计 */}
            <div className="grid grid-cols-1 gap-4 mb-6">
              <Card size="small" className="text-center bg-gradient-to-r from-blue-50 to-purple-50">
                <Statistic
                  title="当前总分"
                  value={getTotalScore()}
                  suffix={`/ ${mockConfigureData.questions.reduce((sum, q) => sum + q.points, 0)}`}
                  valueStyle={{ color: '#1677ff', fontSize: '24px', fontWeight: 'bold' }}
                />
              </Card>
            </div>

            {/* 各题得分 */}
            <div className="space-y-3 mb-6">
              <h4 className="font-medium">各题得分详情</h4>
              {mockConfigureData.questions.map(question => (
                <div key={question.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium">{question.title.split('：')[0]}</span>
                  <div className="flex items-center gap-2">
                    <Tag color={manualScores[question.id] !== undefined ? 'green' : 'blue'}>
                      {getQuestionScore(question.id)}/{question.points}
                    </Tag>
                    {manualScores[question.id] !== undefined && (
                      <Tag color="green">已评</Tag>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <Button
                type="primary"
                size="large"
                icon={<CheckCircleOutlined />}
                onClick={handleSubmitGrading}
                loading={loading}
                block
              >
                提交评分
              </Button>
              
              <div className="grid grid-cols-2 gap-2">
                <Button
                  icon={<SaveOutlined />}
                  onClick={() => message.success('评分已暂存')}
                >
                  暂存评分
                </Button>
                <Button
                  icon={<FastForwardOutlined />}
                  onClick={handleSkipStudent}
                >
                  跳过此份
                </Button>
              </div>
            </div>

            {/* 评分说明 */}
            <Alert
              message="评分说明"
              description={
                <div className="text-sm space-y-1">
                  <p>• AI已完成初步评分，您可以参考或调整</p>
                  <p>• 点击"采用AI建议"快速应用AI评分</p>
                  <p>• 手动调整分数后会覆盖AI评分</p>
                  <p>• 建议逐题评阅，确保评分准确性</p>
                </div>
              }
              type="info"
              showIcon
              className="mt-4"
            />
          </Card>
        </Col>
      </Row>

      {/* 确认提交模态框 */}
      <Modal
        title="确认提交评分"
        open={showConfirmModal}
        onOk={confirmSubmitGrading}
        onCancel={() => setShowConfirmModal(false)}
        okText="确认提交"
        cancelText="取消"
        confirmLoading={loading}
      >
        <div className="space-y-4">
          <p>即将提交 <strong>{currentStudent.name}</strong> 的评分结果：</p>
          <div className="bg-gray-50 p-4 rounded-lg">
            {mockConfigureData.questions.map(question => (
              <div key={question.id} className="flex justify-between items-center py-2">
                <span>{question.title.split('：')[0]}</span>
                <span className="font-medium">{getQuestionScore(question.id)}/{question.points}</span>
              </div>
            ))}
            <div className="border-t pt-2 mt-2 flex justify-between items-center font-bold">
              <span>总分</span>
              <span>{getTotalScore()}/{mockConfigureData.questions.reduce((sum, q) => sum + q.points, 0)}</span>
            </div>
          </div>
          <p className="text-sm text-gray-500">提交后将无法修改，请确认评分准确。</p>
        </div>
      </Modal>
    </div>
  );
};

export default MarkingWorkspace;